import requests
import random
import json
import time
import argparse
import sys
import os
from dotenv import load_dotenv
from upload_to_supabase import upload_to_supabase, process_single_transcript

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), ".env"))

# VAPI API Keys
PUBLIC_VAPI_KEY  = os.environ.get("PUBLIC_VAPI_KEY")
PRIVATE_VAPI_KEY = os.environ.get("PRIVATE_VAPI_KEY")

missing_env = [k for k, v in {
    "PUBLIC_VAPI_KEY": PUBLIC_VAPI_KEY,
    "PRIVATE_VAPI_KEY": PRIVATE_VAPI_KEY
}.items() if not v]
if missing_env:  #  early, explicit failure
    raise RuntimeError(
        f"Missing required environment variable(s): {', '.join(missing_env)}. "
        "Check your .env file or deployment secrets."
    )

missing_env = [k for k, v in {
    "PUBLIC_VAPI_KEY": PUBLIC_VAPI_KEY,
    "PRIVATE_VAPI_KEY": PRIVATE_VAPI_KEY
}.items() if not v]
if missing_env:  #  early, explicit failure
    raise RuntimeError(
        f"Missing required environment variable(s): {', '.join(missing_env)}. "
        "Check your .env file or deployment secrets."
    )

# Default phone number to call
DEFAULT_TO_NUMBER = "+919398760681"

# VAPI API base URL
VAPI_API_BASE_URL = "https://api.vapi.ai"

# Headers for API requests
headers = {
    "Authorization": f"Bearer {PRIVATE_VAPI_KEY}",
    "Content-Type": "application/json"
}

# Set up argument parser
def parse_arguments():
    parser = argparse.ArgumentParser(description='Place a call using VAPI with a random assistant')
    parser.add_argument('--to', dest='to_number', type=str, default=DEFAULT_TO_NUMBER,
                        help=f'Phone number to call (default: {DEFAULT_TO_NUMBER})')
    parser.add_argument('--debug', dest='debug', action='store_true',
                        help='Enable debug mode with verbose output')
    parser.add_argument('--assistant-id', dest='assistant_id', type=str, default="711e3821-8040-496c-a760-23676f0db2d6",
                        help='Specific assistant ID to use')
    parser.add_argument('--phone-id', dest='phone_id', type=str, default="3c14ca41-bd73-49d9-9bce-6e4956834a3d",
                        help='Specific phone number ID to use')
    parser.add_argument('--monitor', dest='monitor', action='store_true', default=True,
                        help='Monitor the call status until it completes')
    parser.add_argument('--get-transcript', dest='get_transcript', type=str,
                        help='Get transcript for a specific call ID')

    return parser.parse_args()

def get_all_assistants(debug=False):
    """Retrieve all assistants from VAPI"""
    url = f"{VAPI_API_BASE_URL}/assistant"

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors

        assistants = response.json()
        print(f"Successfully retrieved {len(assistants)} assistants")

        # Debug: Print full assistant data
        if debug and assistants and len(assistants) > 0:
            print("Assistant data sample:")
            print(json.dumps(assistants[0], indent=2))

        return assistants
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving assistants: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return []

def get_phone_numbers(debug=False):
    """Retrieve all phone numbers from VAPI"""
    url = f"{VAPI_API_BASE_URL}/phone-number"

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()

        phone_numbers = response.json()
        print(f"Successfully retrieved {len(phone_numbers)} phone numbers")

        # Debug: Print full phone number data
        if debug and phone_numbers and len(phone_numbers) > 0:
            print("Phone number data sample:")
            print(json.dumps(phone_numbers[0], indent=2))

        return phone_numbers
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving phone numbers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return []

def place_call(assistant_id, from_number_id, to_number, debug=False):
    """Place a call using a specific assistant and phone number"""
    url = f"{VAPI_API_BASE_URL}/call"

    # Check if assistant_id is valid
    if not assistant_id:
        print("Error: Assistant ID is missing or invalid")
        return None

    payload = {
        "type": "outboundPhoneCall",
        "assistantId": assistant_id,
        "phoneNumberId": from_number_id,
        "customer": {
            "number": to_number
        }
    }

    # Debug: Print the payload
    if debug:
        print(f"Request payload: {json.dumps(payload, indent=2)}")

    try:
        response = requests.post(url, headers=headers, json=payload)

        # Try to get JSON response even if status code is not 200
        try:
            response_json = response.json()
            if debug:
                print(f"Response status: {response.status_code}")
                print(f"Response body: {json.dumps(response_json, indent=2)}")
        except:
            if debug:
                print(f"Response status: {response.status_code}")
                print(f"Response text: {response.text}")

        response.raise_for_status()

        call_data = response.json()
        print(f"Successfully placed call with ID: {call_data.get('id')}")
        return call_data
    except requests.exceptions.RequestException as e:
        print(f"Error placing call: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_call_status(call_id):
    """Get the current status of a call"""
    call_status_url = f"{VAPI_API_BASE_URL}/call/{call_id}"

    try:
        status_response = requests.get(call_status_url, headers=headers)
        status_response.raise_for_status()

        call_data = status_response.json()
        return call_data
    except requests.exceptions.RequestException as e:
        print(f"Error checking call status: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_call_transcript(call_id):
    """Get the transcript for a completed call"""
    # First get the full call data
    call_data = get_call_status(call_id)

    if not call_data:
        print(f"Error: Could not retrieve call data for call ID: {call_id}")
        return None

    # Check if the call has a transcript in the artifact
    if call_data.get('artifact') and call_data['artifact'].get('transcript'):
        return call_data

    # If no transcript is found but the call is completed, try to fetch it again
    # Sometimes the transcript takes a moment to be processed after the call ends
    if call_data.get('status') in ['completed', 'ended']:
        print("Call is completed but no transcript found. Waiting 5 seconds and trying again...")
        time.sleep(5)

        # Try again
        call_data = get_call_status(call_id)
        if call_data and call_data.get('artifact') and call_data['artifact'].get('transcript'):
            print("Successfully retrieved transcript on second attempt.")
            return call_data

    # Return whatever we have, even if transcript is missing
    return call_data

def save_transcript(call_data, call_id):
    """Save the call transcript to a file"""
    try:
        # Create a transcripts directory if it doesn't exist
        transcripts_dir = os.path.join(os.path.dirname(__file__), 'transcripts')
        os.makedirs(transcripts_dir, exist_ok=True)

        # Get current timestamp for filename
        timestamp = time.strftime('%Y%m%d_%H%M%S')

        # Save the full call data as JSON
        json_filename = os.path.join(transcripts_dir, f'transcript_{call_id}_{timestamp}.json')
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(call_data, f, indent=2, ensure_ascii=False)

        # Extract and save a human-readable transcript
        txt_filename = os.path.join(transcripts_dir, f'transcript_{call_id}_{timestamp}.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            # Write header information
            f.write(f"Call ID: {call_id}\n")
            f.write(f"Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Status: {call_data.get('status', 'Unknown')}\n")
            duration = call_data.get('duration', 'Unknown')
            f.write(f"Duration: {duration} seconds\n")
            f.write(f"Cost: ${call_data.get('cost', 0)}\n\n")
            f.write("=" * 50 + "\n\n")

            # Try to extract transcript from different possible locations in the API response
            transcript = None

            # Check if there's a direct transcript field
            if call_data.get('artifact') and call_data['artifact'].get('transcript'):
                transcript = call_data['artifact']['transcript']
                f.write(transcript)

            # If no direct transcript, try to build one from messages
            elif call_data.get('messages'):
                f.write("CONVERSATION:\n\n")
                for msg in call_data['messages']:
                    role = msg.get('role', 'Unknown')
                    message = msg.get('message', '')
                    time_stamp = msg.get('time', '')

                    try:
                        time_val = float(time_stamp)
                        time_str = f"[{time_val:.2f}s]"
                    except (TypeError, ValueError):
                        time_str = f"[{time_stamp}]"
                    else:
                        time_str = ""

                    f.write(f"{time_str} {role.upper()}: {message}\n\n")

            # If artifact has messages, use those too
            elif call_data.get('artifact') and call_data['artifact'].get('messages'):
                f.write("CONVERSATION:\n\n")
                for msg in call_data['artifact']['messages']:
                    role = msg.get('role', 'Unknown')
                    message = msg.get('message', '')
                    time_stamp = msg.get('time', '')

                    if time_stamp:
                        time_str = f"[{time_stamp:.2f}s]"
                    else:
                        time_str = ""

                    f.write(f"{time_str} {role.upper()}: {message}\n\n")

            # If we couldn't find any transcript data
            else:
                f.write("No transcript data found in the call response.\n")
                f.write("Please check the JSON file for complete call data.\n")

        print(f"\nTranscript saved to: {json_filename}")
        print(f"Human-readable transcript saved to: {txt_filename}")

        # Upload transcript to Supabase
        try:
            print("\nUploading transcript to Supabase...")
            if process_single_transcript(json_filename):
                print("Successfully uploaded transcript to Supabase voicebot_data table")
            else:
                print("Failed to upload transcript to Supabase")
        except Exception as e:
            print(f"Error uploading transcript to Supabase: {e}")

        return json_filename
    except Exception as e:
        print(f"Error saving transcript: {e}")
        return None

def monitor_call(call_id, interval=5, max_time=300, debug=False):
    """Monitor a call until it completes or max_time is reached"""
    print(f"\nMonitoring call {call_id}...")
    start_time = time.time()
    last_status = None

    while time.time() - start_time < max_time:
        call_data = get_call_status(call_id)

        if not call_data:
            print("Failed to get call status. Stopping monitoring.")
            return

        current_status = call_data.get('status')

        # Only print if status has changed
        if current_status != last_status:
            print(f"Call status: {current_status} (at {time.strftime('%H:%M:%S')})")
            last_status = current_status

        # Check if call has ended
        if current_status in ['completed', 'ended', 'failed', 'busy', 'no-answer', 'canceled']:
            print(f"\nCall has ended with status: {current_status}")
            if call_data.get('endedReason'):
                print(f"End reason: {call_data.get('endedReason')}")
            if call_data.get('cost'):
                print(f"Call cost: ${call_data.get('cost')}")

            # Debug: Print call data to see what we're working with
            if debug:
                print("\nCall data received:")
                print(json.dumps(call_data, indent=2))

            # Save the transcript when call ends (trying for both 'completed' and 'ended' statuses)
            transcript_saved = False
            if current_status in ['completed', 'ended']:
                print("\nAttempting to save transcript...")

                # Try to get the most complete transcript data
                print("Fetching complete transcript data...")
                transcript_data = get_call_transcript(call_id)

                # Use the transcript data if available, otherwise use the current call data
                data_to_save = transcript_data if transcript_data else call_data

                transcript_file = save_transcript(data_to_save, call_id)
                if transcript_file:
                    print(f"Call transcript saved to: {transcript_file}")
                    transcript_saved = True
                else:
                    print("Failed to save call transcript")

            # If we couldn't save transcript but have transcript data, try to save it anyway
            if not transcript_saved and call_data.get('transcript'):
                print("\nTrying to save transcript from transcript data...")
                transcript_file = save_transcript(call_data, call_id)
                if transcript_file:
                    print(f"Call transcript saved to: {transcript_file}")

            return call_data  # Return call data for further processing if needed

        # Wait before checking again
        time.sleep(interval)

    print(f"\nReached maximum monitoring time of {max_time} seconds. Call may still be in progress.")
    return None

def main():
    # Parse command line arguments
    args = parse_arguments()
    to_number = args.to_number
    debug_mode = args.debug

    # Check if we're just retrieving a transcript for a past call
    if args.get_transcript:
        call_id = args.get_transcript
        print(f"\nRetrieving transcript for call ID: {call_id}")

        # Get the call data with transcript
        call_data = get_call_transcript(call_id)

        if call_data:
            # Save the transcript
            transcript_file = save_transcript(call_data, call_id)
            if transcript_file:
                print(f"\nTranscript for call {call_id} saved successfully.")
            else:
                print(f"\nFailed to save transcript for call {call_id}.")
        else:
            print(f"\nFailed to retrieve call data for call ID: {call_id}")

        return  # Exit after retrieving transcript

    # Use the assistant ID from arguments (which has a new default)
    assistant_id = args.assistant_id
    # We'll fetch assistant details later if needed for name, or just use ID
    assistant_name = f"Assistant ID {assistant_id}" 

    print(f"\nUsing assistant: {assistant_name}")

    # Get phone numbers
    phone_numbers = get_phone_numbers(debug=debug_mode)
    if not phone_numbers:
        print("No phone numbers found. Please add a phone number in the VAPI dashboard.")
        return

    # Print all phone numbers for reference
    print("\nAvailable Phone Numbers:")
    for i, phone in enumerate(phone_numbers):
        phone_id = phone.get('id')
        phone_number = phone.get('twilioPhoneNumber', 'Unknown')
        print(f"{i+1}. {phone_number} (ID: {phone_id})")

    # Use specified phone ID or use the first available (now args.phone_id has a default)
    if args.phone_id:
        # Find the phone with the specified ID
        selected_phone = next((p for p in phone_numbers if p.get('id') == args.phone_id), None)

        if not selected_phone:
            print(f"Phone number with ID '{args.phone_id}' not found. Defaulting to first available or please check the ID and try again.")
            # Fallback to first phone if specified default ID is not found
            if phone_numbers:
                phone_number_id = phone_numbers[0].get('id')
                phone_number = phone_numbers[0].get('twilioPhoneNumber', 'Unknown')
                print(f"\nUsing first available phone number: {phone_number} (ID: {phone_number_id})")
            else:
                print("No phone numbers available.")
                return
        else:
            phone_number_id = args.phone_id
            phone_number = selected_phone.get('twilioPhoneNumber', 'Unknown')
            print(f"\nUsing specified phone number: {phone_number} (ID: {phone_number_id})")
    else: # Should not be reached if phone_id has a default, but kept for safety
        # Use the first phone number
        phone_number_id = phone_numbers[0].get('id')
        phone_number = phone_numbers[0].get('twilioPhoneNumber', 'Unknown')
        print(f"\nUsing first available phone number: {phone_number} (ID: {phone_number_id})")

    # Place the call
    print(f"\nPlacing call to {to_number} using assistant '{assistant_name}'...")
    call_data = place_call(assistant_id, phone_number_id, to_number, debug=debug_mode)

    if call_data:
        print("\nCall placed successfully!")
        print(f"Call ID: {call_data.get('id')}")
        print(f"Call Status: {call_data.get('status')}")

        call_id = call_data.get('id')

        if args.monitor:
            # Monitor the call until it completes
            monitor_call(call_id, debug=debug_mode)
        else:
            # Just check status once after a short delay
            print("\nWaiting for 5 seconds to check call status...")
            time.sleep(5)

            updated_call_data = get_call_status(call_id)
            if updated_call_data:
                print(f"\nUpdated Call Status: {updated_call_data.get('status')}")
                print("\nTo monitor this call until completion, run:")
                print(f"python app.py --assistant-id {assistant_id} --phone-id {phone_number_id} --to {to_number} --monitor")
    else:
        print("\nFailed to place call. Please check the error messages above.")

if __name__ == "__main__":
    main()