<style>
  /* styles.css */
  .no-hover-bg:hover {
    background-color: transparent !important;
  }

  /* Remove all effects from the close button */
  .no-hover-effect {
    background: none !important;
    background-color: transparent !important;
    transition: none !important;
    position: absolute;
    top: 20px;
    right: 10px;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    cursor: pointer;
    z-index: 10;
    box-shadow: none !important;
    outline: none !important;
  }

  .no-hover-effect:hover,
  .no-hover-effect:focus,
  .no-hover-effect:active {
    background: none !important;
    background-color: transparent !important;
    transform: none !important;
    filter: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  /* Ensure the SVG inside the close button uses the correct color based on theme */
  .light .no-hover-effect svg {
    stroke: #000000;
    background: none !important;
  }

  .pure-black .no-hover-effect svg {
    stroke: #ffffff;
    background: none !important;
  }

  /* Remove any background from the SVG */
  .no-hover-effect svg {
    background: none !important;
    box-shadow: none !important;
  }

  /* Override any UIkit default styles for close buttons */
  .uk-modal-close {
    background: none !important;
    box-shadow: none !important;
  }

  .uk-modal-close::before,
  .uk-modal-close::after {
    display: none !important;
  }

  /* Ensure no background on the button in any state */
  button.no-hover-effect {
    background-image: none !important;
  }

  /* Export modal title styling */
  #export-modal h2 {
    color: var(--foreground);
    letter-spacing: 0.01em;
    font-weight: 400;
    opacity: 0.9;
    line-height: 24px;
    /* Match the height of the X icon */
  }

  /* Simple styling for topright */
  .uk-navbar-item {
    display: flex;
    align-items: center;
  }

  /* Center the modal and set dimensions */
  #export-modal .uk-modal-dialog {
    width: 420px;
    height: 450px;
    display: flex;
    /* Use flexbox for centering content if needed */
    flex-direction: column;
    /* Stack content vertically */
  }

  /* Ensure modal is centered vertically and horizontally */
  .uk-modal.uk-open {
    display: flex !important;
    justify-content: center;
    align-items: center;
  }



  /* Container styling when modal is open - no blur effect */
  body.uk-modal-page div.relative {
    /* Target the main container */
    /* Removed blur filter */
    transition: all 0.2s ease-in-out;
    /* Keep smooth transition */
    /* Prevent interaction with background elements */
    pointer-events: none;
  }

  /* Ensure the modal dialog itself is not blurred and remains interactive */
  body.uk-modal-page #export-modal .uk-modal-dialog {
    filter: none !important;
    pointer-events: auto !important;
    /* Allow interaction ONLY with the modal dialog */
  }



  /* Ensure elements *inside* the modal dialog are also interactive */
  body.uk-modal-page #export-modal .uk-modal-dialog * {
    pointer-events: auto !important;
  }

  /* Ensure the modal overlay doesn't block interaction with the dialog */
  .uk-modal {
    pointer-events: none;
    /* Overlay doesn't capture clicks */
  }

  /* Override for export modal to allow glassmorphic effect */
  #export-modal.uk-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .uk-modal-dialog {
    pointer-events: auto;
    /* Dialog captures clicks */
  }

  /* Override any UIkit default styles that might apply blur */
  .uk-modal-overlay,
  .uk-modal-page::before,
  .uk-modal-page::after,
  body.uk-modal-page {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
  }

  /* Ensure modal dialog has solid background in both themes */
  .light .uk-modal-dialog {
    background-color: #ffffff !important;
  }

  .pure-black .uk-modal-dialog {
    background-color: #09090b !important;
  }

  /* Specific rule for export modal */
  #export-modal .uk-modal-dialog {
    background-color: var(--popup-bg, #ffffff) !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  .pure-black #export-modal .uk-modal-dialog {
    background-color: var(--popup-bg, #09090b) !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  /* Ensure card class in modal has solid background */
  #export-modal .uk-modal-dialog.card {
    background-color: var(--popup-bg, #ffffff) !important;
    opacity: 1 !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  .pure-black #export-modal .uk-modal-dialog.card {
    background-color: var(--popup-bg, #09090b) !important;
    opacity: 1 !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  /* Ensure bg-background class in modal has solid background */
  #export-modal .uk-modal-dialog.bg-background {
    background-color: var(--popup-bg, #ffffff) !important;
    opacity: 1 !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  .pure-black #export-modal .uk-modal-dialog.bg-background {
    background-color: var(--popup-bg, #09090b) !important;
    opacity: 1 !important;
    width: 420px !important;
    max-width: 420px !important;
  }

  /* Fix for notification dropdown dimensions */
  #notification-dropdown {
    width: 540px !important;
    /* Force width to be 540px */
    max-width: 540px !important;
    z-index: 1111;
    /* Added z-index for proper stacking context */
  }

  #notification-dropdown .notification-tab-content {
    width: 100% !important;
    /* Ensure all tabs take full width */
  }

  #notification-dropdown .notification-tab-content ul {
    height: 400px !important;
    /* Force height to be 400px */
    width: 100% !important;
  }

  /* Fix for UIkit dropdown width overrides */
  .uk-dropdown-nav.uk-nav {
    width: 100%;
  }

  /* Prevent tab content from collapsing */
  .notification-tab-content {
    min-height: 400px;
    min-width: 540px;
  }
</style>
<script src="https://unpkg.com/franken-ui@1.1.0/dist/js/core.iife.js" type="module"></script>
<script src="https://unpkg.com/franken-ui@1.1.0/dist/js/icon.iife.js" type="module"></script>
<link rel="stylesheet" href="../static/styles/custom.css">


<div class="relative">
  <div class="uk-navbar-item flex items-center">
    <ul style="margin-right: 5px;" class="uk-iconnav uk-iconnav-outline">
      <!-- Removed the standalone Calendar button -->

      <!-- Period Dropdown Button -->
      <li>
        <a href="javascript:void(0);" id="periodOption" class="card flex items-center justify-center universal-hover"
          style="background-color: transparent; padding: 0 12px; min-width: 100px;" aria-label="Select Time Period">
          <uk-icon icon="clock" style="margin-right: 8px;"></uk-icon> <!-- Using clock icon -->
          <span id="currentPeriod" style="font-size: 14px; white-space: nowrap;">
            Period
          </span>
        </a>
        <!-- Period Dropdown -->
        <div class="uk-drop uk-dropdown dropdown-content" id="period-dropdown-content"
          uk-dropdown="mode: click; pos: bottom-right; offset: 10; boundary: !.uk-navbar-item; flip: false">
          <ul class="uk-dropdown-nav uk-nav">
            <!-- Calendar Date Selector Option -->
            <li style="background-color: inherit !important; transition: none !important; transform: none !important;">
              <a href="#" class="period-calendar-option px-2 py-1.5 text-sm justify-between flex items-center"
                id="selectSpecificDate">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                    <line x1="16" x2="16" y1="2" y2="6"></line>
                    <line x1="8" x2="8" y1="2" y2="6"></line>
                    <line x1="3" x2="21" y1="10" y2="10"></line>
                  </svg>
                  <span>Select Date</span>
                </div>
                <span id="currentDate" class="ml-2 text-xs bg-muted/60 px-1.5 py-0.5 rounded">
                  <!-- Date will be inserted by script -->
                </span>
              </a>
              <!-- Calendar element - initially hidden -->
              <!-- Calendar element - initially hidden -->
              <div id="calendar-container" class="hidden p-3 bg-background card"
                style="background-color: inherit !important; transition: none !important; transform: none !important; filter: none !important; box-shadow: none !important; outline: none !important; pointer-events: auto !important;">
                <div id="tailwind-calendar" class=" text-foreground">
                  <div class="flex items-center justify-between mb-2">
                    <button id="prev-month"
                      class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6" />
                      </svg>
                    </button>
                    <div id="month-year" class="font-semibold text-sm"></div>
                    <button id="next-month"
                      class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </button>
                  </div>
                  <div class="grid grid-cols-7 gap-1 text-center text-xs text-muted-foreground mb-1">
                    <div>Su</div>
                    <div>Mo</div>
                    <div>Tu</div>
                    <div>We</div>
                    <div>Th</div>
                    <div>Fr</div>
                    <div>Sa</div>
                  </div>
                  <div id="calendar-grid" class="grid grid-cols-7 gap-1">
                    <!-- Dates will be populated by script -->
                  </div>
                </div>
              </div>
            </li>
            <li class="uk-nav-divider"></li>
            <!-- Existing period options remain unchanged -->
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="today">Today</a></li>
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="this-week">This Week</a></li>
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="this-month">This Month</a></li>
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="last-3-months">Last 3 Months</a></li>
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="last-6-months">Last 6 Months</a></li>
            <li><a href="#" class="period-select-option px-2 py-1.5 text-sm justify-between"
                data-period="last-12-months">Last 12 Months</a></li>
          </ul>
        </div>
      </li>


      <li>
        <!-- Replaced <a> with <button> and added uk-toggle -->
        <button uk-toggle="target: #export-modal"
          class="uk-button uk-button-default flex items-center justify-center card universal-hover"
          style="background-color: transparent; padding: 0 12px; height: 36px; line-height: 30px;"
          aria-label="Download">
          <!-- Using a standard SVG for download/export -->
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-download mr-2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
            <polyline points="7 10 12 15 17 10" />
            <line x1="12" x2="12" y1="15" y2="3" />
          </svg>
          <span style="font-size: 14px; white-space: nowrap;">Export</span>
        </button>
      </li>
      <li>
        <a href="javascript:void(0);" uk-toggle="target: #language-dropdown" id="languageToggler"
          style="background-color: transparent;" class="flex items-center justify-center card universal-hover"
          aria-label="Select Language">
          <uk-icon icon="languages"></uk-icon>
        </a>
        <!-- Dropdown Menu -->
        <div class="card dropdown-content" id="language-dropdown"
          uk-dropdown="mode: click; pos: bottom-right; offset: 10">
          <ul class="uk-nav uk-dropdown-nav card">
            <li><a href="#" class="language-option" data-no-translate data-lang="en">English</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="es">Spanish</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="fr">French</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="it">Italian</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="sv">Swedish</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="de">German</a></li>
            <li><a href="#" class="language-option" data-no-translate data-lang="nl">Dutch</a></li>
          </ul>
        </div>
      </li>

      <!-- Notification Button with Dropdown -->
      <li>
        <a href="javascript:void(0);" id="notificationToggler" style="background-color: transparent;"
          class="flex items-center justify-center card universal-hover" aria-label="Notifications">
          <span class="relative">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-inbox-icon lucide-inbox">
              <polyline points="22 12 16 12 14 15 10 15 8 12 2 12" />
              <path
                d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z" />
            </svg>
          </span>
        </a>
        <!-- Notification Dropdown -->
        <div class="card dropdown-content" id="notification-dropdown">
          <!-- Header with no highlight -->
          <div class="px-4 py-3 flex justify-between items-center">
            <span class="text-sm font-medium">Notifications & History</span>
            <div class="relative">
              <button type="button" id="notificationConfigToggler"
                class="flex items-center justify-center card universal-hover"
                style="background-color: transparent; padding: 4px; border: none; cursor: pointer;"
                aria-label="Notification Options">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                  <circle cx="5" cy="12" r="1"></circle>
                </svg>
              </button>
              <!-- Config Options (not a dropdown) -->
              <div class="card absolute right-0 mt-1 z-50 hidden universal-background-color"
                id="notification-config-options"
                style="min-width: 150px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);">
                <ul class="uk-nav uk-dropdown-nav card p-1 ">
                  <li><a href="#" class="language-option">Check In</a></li>
                  <li><a href="#" class="language-option" data-no-translate data-lang="en">English</a></li>
                  <li><a href="#" class="language-option">Check Out</a></li>
                  <li><a href="#" class="language-option">Tasks</a></li>
                  <li><a href="#" class="language-option">Chat</a></li>
                </ul>
              </div>
            </div>
          </div>

          <hr class="uk-hr card" />

          <!-- Tasks Tab Content -->
          <div id="tasks-tab" class="notification-tab-content">
            <ul class="uk-nav uk-dropdown-nav card h-[400px] w-full overflow-y-auto p-0">
              <li class="hover:bg-muted/50">
                <div class="flex items-start p-3">
                  <div
                    class="h-8 w-8 rounded-full bg-amber-500/10 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="text-amber-500">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <line x1="10" y1="9" x2="8" y2="9"></line>
                    </svg>
                  </div>
                  <div class="flex-grow">
                    <p class="text-sm m-0 flex items-center justify-between">
                      <span class="font-medium">Room inspection needed</span>
                      <span class="text-xs bg-amber-100 text-amber-800 px-1.5 py-0.5 rounded">Medium</span>
                    </p>
                    <p class="text-xs text-muted-foreground mt-1 mb-0">Room 204 • Due in 2 hours</p>
                  </div>
                </div>
              </li>

              <hr class="uk-hr card" />

              <li class="hover:bg-muted/50">
                <div class="flex items-start p-3">
                  <div
                    class="h-8 w-8 rounded-full bg-red-500/10 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="text-red-500">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <line x1="10" y1="9" x2="8" y2="9"></line>
                    </svg>
                  </div>
                  <div class="flex-grow">
                    <p class="text-sm m-0 flex items-center justify-between">
                      <span class="font-medium">Maintenance request</span>
                      <span class="text-xs bg-red-100 text-red-800 px-1.5 py-0.5 rounded">Urgent</span>
                    </p>
                    <p class="text-xs text-muted-foreground mt-1 mb-0">Room 118 • AC repair • Overdue</p>
                  </div>
                </div>
              </li>

              <hr class="uk-hr card" />

              <li class="hover:bg-muted/50">
                <div class="flex items-start p-3">
                  <div
                    class="h-8 w-8 rounded-full bg-green-500/10 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="text-green-500">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <line x1="10" y1="9" x2="8" y2="9"></line>
                    </svg>
                  </div>
                  <div class="flex-grow">
                    <p class="text-sm m-0 flex items-center justify-between">
                      <span class="font-medium">Room cleaning</span>
                      <span class="text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded">Low</span>
                    </p>
                    <p class="text-xs text-muted-foreground mt-1 mb-0">Room 312 • Due tomorrow</p>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </li>

      <script>
        // Completely rewritten notification system with manual control
        document.addEventListener('DOMContentLoaded', function () {
          // Main elements
          const notificationToggler = document.getElementById('notificationToggler');
          const notificationDropdown = document.getElementById('notification-dropdown');
          const configToggler = document.getElementById('notificationConfigToggler');
          const configOptions = document.getElementById('notification-config-options');
          const tasksContent = document.getElementById('tasks-tab');

          // Variables to track state
          let isMainDropdownOpen = false;
          let isConfigOptionsOpen = false;
          let clickedInsideConfig = false;

          // Set fixed dimensions for the notification dropdown
          function setNotificationDimensions() {
            notificationDropdown.style.width = '540px';
            notificationDropdown.style.maxWidth = '540px';

            if (tasksContent) {
              tasksContent.style.minWidth = '540px';
              tasksContent.style.width = '540px';

              const contentList = tasksContent.querySelector('ul');
              if (contentList) {
                contentList.style.height = '400px';
              }
            }
          }

          // Initialize UIkit dropdown for the main notification dropdown only
          if (notificationDropdown && UIkit.dropdown) {
            const mainDropdown = UIkit.dropdown(notificationDropdown, {
              mode: 'click',
              pos: 'bottom-right',
              offset: 10,
              animation: ['uk-animation-fade', 'uk-animation-slide-top-small'],
              duration: 200,
              boundary: document.querySelector('.uk-navbar-item')
            });

            // Set dimensions when dropdown is shown
            UIkit.util.on(notificationDropdown, 'show', function () {
              isMainDropdownOpen = true;
              setNotificationDimensions();
            });

            // Update state when dropdown is hidden
            UIkit.util.on(notificationDropdown, 'hide', function () {
              isMainDropdownOpen = false;
              // Also hide config options if they're open
              if (isConfigOptionsOpen) {
                configOptions.classList.add('hidden');
                isConfigOptionsOpen = false;
              }
            });
          }

          // Manual handling for the config options menu
          if (configToggler && configOptions) {
            // Toggle config options when clicking the config button
            configToggler.addEventListener('click', function (e) {
              e.stopPropagation(); // Prevent event from bubbling to parent elements

              // Toggle the config options visibility
              if (configOptions.classList.contains('hidden')) {
                configOptions.classList.remove('hidden');
                isConfigOptionsOpen = true;
                clickedInsideConfig = true;

                // Reset the flag after a short delay
                setTimeout(() => {
                  clickedInsideConfig = false;
                }, 10);
              } else {
                configOptions.classList.add('hidden');
                isConfigOptionsOpen = false;
              }
            });

            // Prevent clicks inside config options from closing the main dropdown
            configOptions.addEventListener('click', function (e) {
              e.stopPropagation();
              clickedInsideConfig = true;

              // Reset the flag after a short delay
              setTimeout(() => {
                clickedInsideConfig = false;

                // Hide the config options after clicking an option
                configOptions.classList.add('hidden');
                isConfigOptionsOpen = false;
              }, 10);
            });
          }

          // Prevent clicks inside the main dropdown from closing it
          notificationDropdown.addEventListener('click', function (e) {
            // Only stop propagation if not clicking the config toggler
            if (e.target !== configToggler && !configToggler.contains(e.target)) {
              e.stopPropagation();
            }
          });

          // Global document click handler to close dropdowns when clicking outside
          document.addEventListener('click', function (e) {
            // Close config options when clicking outside
            if (isConfigOptionsOpen && !configToggler.contains(e.target) && !configOptions.contains(e.target) && !clickedInsideConfig) {
              configOptions.classList.add('hidden');
              isConfigOptionsOpen = false;
            }

            // Let UIkit handle the main dropdown closing
          });

          // Handle escape key to close dropdowns
          document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
              // Close config options first if they're open
              if (isConfigOptionsOpen) {
                configOptions.classList.add('hidden');
                isConfigOptionsOpen = false;
                e.stopPropagation(); // Prevent the event from also closing the main dropdown
              }
              // Let UIkit handle the main dropdown closing
            }
          });
        });
      </script>

      <li>
        <a href="javascript:void(0);" id="darkModeToggler" class="card flex items-center justify-center universal-hover"
          style="background-color: transparent;" aria-label="Toggle Dark Mode">
          <uk-icon id="moonIcon" icon="moon-star"></uk-icon>
          <uk-icon id="sunIcon" icon="sun" class="hidden"></uk-icon>
        </a>
      </li>

      <!-- Add this script at the end of the file -->
      <!-- ...existing code... -->

      <script>
        // Set current date on load
        document.addEventListener('DOMContentLoaded', function () {
          const today = new Date();
          const options = { year: 'numeric', month: 'short', day: 'numeric' };
          const formattedDate = today.toLocaleDateString(undefined, options);
          const mainDateDisplay = document.getElementById('currentDate');
          mainDateDisplay.textContent = formattedDate;

          // --- Calendar Logic ---
          const calendarGrid = document.getElementById('calendar-grid');
          const monthYearDisplay = document.getElementById('month-year');
          const prevMonthButton = document.getElementById('prev-month');
          const nextMonthButton = document.getElementById('next-month');
          const calendarContainer = document.getElementById('calendar-container');
          const selectDateOption = document.getElementById('selectSpecificDate');

          // --- Period Selector Logic ---
          const periodDisplay = document.getElementById('currentPeriod');
          const periodDropdownElement = document.getElementById('period-dropdown-content');
          const periodOptions = document.querySelectorAll('.period-select-option');
          const periodOptionButton = document.getElementById('periodOption');
          const defaultPeriodMinWidth = '100px';
          let selectedPeriodValue = null;

          let currentDate = new Date();
          let selectedDate = new Date();

          // Toggle calendar visibility when clicking "Select Date" option
          selectDateOption.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle calendar container visibility
            if (calendarContainer.classList.contains('hidden')) {
              calendarContainer.classList.remove('hidden');
              renderCalendar(currentDate);
            } else {
              calendarContainer.classList.add('hidden');
            }
          });

          // Prevent dropdown from closing when clicking inside calendar
          calendarContainer.addEventListener('click', function (e) {
            e.stopPropagation();
          });

          function renderCalendar(date) {
            calendarGrid.innerHTML = ''; // Clear previous grid
            const year = date.getFullYear();
            const month = date.getMonth(); // 0-indexed

            monthYearDisplay.textContent = date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' });

            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);
            const daysInMonth = lastDayOfMonth.getDate();
            const startDayOfWeek = firstDayOfMonth.getDay(); // 0 for Sunday, 1 for Monday, etc.

            // Add empty cells for days before the 1st of the month
            for (let i = 0; i < startDayOfWeek; i++) {
              const emptyCell = document.createElement('div');
              calendarGrid.appendChild(emptyCell);
            }

            // Add date cells
            for (let day = 1; day <= daysInMonth; day++) {
              const dateCell = document.createElement('button');
              dateCell.textContent = day;
              dateCell.classList.add('text-sm', 'p-1', 'rounded', 'hover:bg-primary', 'hover:text-primary-foreground', 'focus:outline-none', 'focus:ring-2', 'focus:ring-ring', 'focus:z-10');

              const cellDate = new Date(year, month, day);

              // Highlight today's date
              const todayDate = new Date();
              if (cellDate.toDateString() === todayDate.toDateString()) {
                dateCell.classList.add('bg-muted', 'font-semibold');
              }

              // Highlight selected date only if a specific date is selected (not a period)
              if (selectedDate && !selectedPeriodValue && cellDate.toDateString() === selectedDate.toDateString()) {
                dateCell.classList.add('bg-primary', 'text-primary-foreground', 'font-bold');
                dateCell.classList.remove('hover:bg-primary', 'hover:text-primary-foreground'); // Avoid double styling
              }

              dateCell.addEventListener('click', () => {
                selectedDate = cellDate;
                selectedPeriodValue = null; // Clear period selection
                // Update date display
                mainDateDisplay.textContent = selectedDate.toLocaleDateString(undefined, options);
                // Reset period display text but keep dropdown open
                periodDisplay.textContent = 'Date: ' + selectedDate.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                periodOptionButton.style.minWidth = '140px'; // Adjust width as needed
                // Hide calendar after selection
                calendarContainer.classList.add('hidden');
                // Re-render to show selection highlight
                renderCalendar(currentDate);
                // TODO: Add logic here to filter data based on the selected DATE
                console.log("Selected Date:", selectedDate);
              });
              calendarGrid.appendChild(dateCell);
            }
          }

          prevMonthButton.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar(currentDate);
          });

          nextMonthButton.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar(currentDate);
          });

          // --- Period Selector Event Listeners ---
          periodOptions.forEach(option => {
            option.addEventListener('click', (e) => {
              e.preventDefault();
              const periodText = option.textContent;
              const periodValue = option.getAttribute('data-period');

              selectedPeriodValue = periodValue; // Store the period value
              selectedDate = null; // Clear specific date selection

              // Update ONLY the period button display
              periodDisplay.textContent = periodText;

              // --- Adjust min-width based on selection ---
              let newMinWidth = defaultPeriodMinWidth; // Default
              switch (periodValue) {
                case 'this-week':
                  newMinWidth = '120px';
                  break;
                case 'this-month':
                  newMinWidth = '125px';
                  break;
                case 'last-3-months':
                  newMinWidth = '135px';
                  break;
                case 'last-6-months':
                  newMinWidth = '135px';
                  break;
                case 'last-12-months':
                  newMinWidth = '140px';
                  break;
                // 'today' uses the default 100px
              }
              periodOptionButton.style.minWidth = newMinWidth;
              // --- End width adjustment ---

              // Hide the calendar if it's visible
              calendarContainer.classList.add('hidden');

              // Close the period dropdown
              if (periodDropdownElement && UIkit.dropdown(periodDropdownElement)) {
                UIkit.dropdown(periodDropdownElement).hide(false);
              }

              // Re-render calendar to remove specific date highlight
              renderCalendar(currentDate);

              // TODO: Add logic here to filter data based on the selected PERIOD
              console.log("Selected Period:", selectedPeriodValue);
            });
          });

          // Initial render
          renderCalendar(currentDate);
          // Set initial selected date display
          mainDateDisplay.textContent = selectedDate.toLocaleDateString(undefined, options);
          // Set initial period display and width
          periodDisplay.textContent = 'Period';
          periodOptionButton.style.minWidth = defaultPeriodMinWidth; // Set initial width
        });
      </script>

      <!-- ...existing code... -->

    </ul>

    <!-- Export Modal Structure -->
    <!-- Replace the export modal section with this improved version -->

    <!-- Export Modal Structure -->
    <div id="export-modal" uk-modal
      style="backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(0, 0, 0, 0.5);">
      <div class="uk-modal-dialog card bg-background text-foreground overflow-visible"
        style="border-radius: 12px; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1); width: 420px; max-width: 95vw; height: 450px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none; position: relative; overflow: visible;">
        <style>
          #export-modal .uk-modal-dialog::-webkit-scrollbar {
            display: none;
          }

          /* Fixed footer for buttons */
          .export-modal-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background-color: var(--popup-bg);
            border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
            display: flex;
            justify-content: space-between;
            z-index: 10;
            border-radius: 0 0 12px 12px;
          }

          /* Add padding to the bottom of the content to prevent overlap with fixed footer */
          .export-modal-content {
            padding-bottom: 70px;
          }
        </style>

        <!-- Header with close button -->
        <div class="flex card items-center justify-between p-5 border-b border-border/40">
          <h2 class="text-lg font-medium m-0 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-primary">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7 10 12 15 17 10" />
              <line x1="12" x2="12" y1="15" y2="3" />
            </svg>
            Export Data
          </h2>
          <button class="uk-modal-close no-hover-effect" type="button">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </div>

        <!-- Container content -->
        <div class="p-4 export-modal-content">
          <!-- Date Range Selection -->
          <div class="mb-5">
            <label class="block text-base font-medium mb-3 text-foreground/80">Date Range</label>
            <div class="flex items-center gap-2 mb-3">
              <!-- Date inputs with improved styling -->
              <div class="relative flex-1">
                <input type="text" id="export-start-date"
                  class="w-full px-3 py-2 rounded-md border border-border/60 card focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary cursor-pointer text-sm"
                  placeholder="Start date" readonly>
                <svg class="absolute right-3 top-2.5 text-muted-foreground pointer-events-none"
                  xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                  <line x1="16" x2="16" y1="2" y2="6"></line>
                  <line x1="8" x2="8" y1="2" y2="6"></line>
                  <line x1="3" x2="21" y1="10" y2="10"></line>
                </svg>
              </div>
              <span class="text-muted-foreground">to</span>
              <div class="relative flex-1">
                <input type="text" id="export-end-date"
                  class="w-full px-3 py-2 rounded-md border border-border/60 card focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary cursor-pointer text-sm"
                  placeholder="End date" readonly>
                <svg class="absolute right-3 top-2.5 text-muted-foreground pointer-events-none"
                  xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                  <line x1="16" x2="16" y1="2" y2="6"></line>
                  <line x1="8" x2="8" y1="2" y2="6"></line>
                  <line x1="3" x2="21" y1="10" y2="10"></line>
                </svg>
              </div>
            </div>

            <!-- Date Presets with improved styling -->
            <div class="flex flex-wrap gap-1.5">
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-days="7">Last 7 days</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-days="30">Last 30 days</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-range="this-month">This month</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-range="last-month">Last month</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-range="all-time">All time</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-days="1">Yesterday</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-days="90">Last 90 days</button>
              <button
                class="export-date-preset text-xs px-3 py-1.5 rounded-md card border border-border/60 hover:bg-muted/50 focus:ring-1 focus:ring-primary focus:outline-none transition-colors"
                data-range="this-year">This Year</button>
            </div>
          </div>

          <!-- Calendar Popup for date selection - kept hidden but improved styling -->
          <div id="export-calendar"
            class="card p-3 mb-4 hidden w-64 text-foreground universal-background-color border border-border/60 rounded-md"
            style="position: absolute; z-index: 50; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);">
            <div class="flex items-center justify-between mb-2">
              <button id="export-prev-month"
                class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-1 focus:ring-primary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6" />
                </svg>
              </button>
              <div id="export-month-year" class="font-semibold text-sm"></div>
              <button id="export-next-month"
                class="p-1 rounded hover:bg-muted focus:outline-none focus:ring-1 focus:ring-primary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </button>
            </div>
            <div class="grid grid-cols-7 gap-1 text-center text-xs text-muted-foreground mb-1">
              <div>Su</div>
              <div>Mo</div>
              <div>Tu</div>
              <div>We</div>
              <div>Th</div>
              <div>Fr</div>
              <div>Sa</div>
            </div>
            <div id="export-calendar-grid" class="grid grid-cols-7 gap-1">
              <!-- Dates will be populated by script -->
            </div>
            <div class="flex justify-between mt-3">
              <button id="export-cancel-date"
                class="text-xs px-3 py-1.5 card rounded-md border border-border/60 hover:bg-muted/50 focus:outline-none focus:ring-1 focus:ring-primary transition-colors">Cancel</button>
              <button id="export-apply-date"
                class="text-xs px-3 py-1.5 card rounded-md bg-primary text-primary-foreground hover:bg-primary/90 focus:outline-none focus:ring-1 focus:ring-primary transition-colors">Apply</button>
            </div>
          </div>

          <!-- Format Selection with improved layout - only CSV and JSON -->
          <div class="mb-6" style="margin-top: 40px;">
            <label class="block text-base font-medium mb-2 text-foreground/80">Export Format</label>
            <div class="grid grid-cols-2 gap-3">
              <!-- CSV Format -->
              <button
                class="export-format-btn flex items-center h-10 px-4 rounded-md border border-border/60 card hover:bg-muted/30 focus:outline-none focus:ring-1 focus:ring-primary transition-colors bg-transparent"
                data-format="csv">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="text-green-600 mr-3">
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  <polyline points="14 2 14 8 20 8" />
                </svg>
                <span class="font-medium text-sm">CSV</span>
              </button>

              <!-- JSON Format -->
              <button
                class="export-format-btn flex items-center h-10 px-4 rounded-md border border-border/60 card hover:bg-muted/30 focus:outline-none focus:ring-1 focus:ring-primary transition-colors bg-transparent"
                data-format="json">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="text-amber-600 mr-3">
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  <polyline points="14 2 14 8 20 8" />
                </svg>
                <span class="font-medium text-sm">JSON</span>
              </button>
            </div>
          </div>

        </div>

        <!-- Fixed footer with buttons -->
        <div class="export-modal-footer">
          <!-- Cancel Button -->
          <button id="cancel-export"
            class="w-[100px] py-2 px-4 bg-red-500 text-white rounded-md border border-border/60 card transition-colors flex items-center justify-center font-medium text-sm hover:bg-red-600 focus:outline-none focus:ring-1 focus:ring-red-500 pure-black-button">
            Cancel
          </button>

          <!-- Export Button -->
          <button id="execute-export" disabled
            class="w-[100px] py-2 px-4 bg-primary text-primary-foreground rounded-md border border-border/60 card opacity-50 transition-colors flex items-center justify-center font-medium text-sm hover:bg-primary/90 focus:outline-none focus:ring-1 focus:ring-primary pure-black-button">
            Export
          </button>

          <div id="export-loading"
            class="hidden w-[100px] py-2 px-4 bg-primary text-primary-foreground rounded-md border border-border/60 card transition-colors flex items-center justify-center font-medium text-sm pure-black-button">
            <svg class="animate-spin mr-2 h-4 w-4 text-primary-foreground" xmlns="http://www.w3.org/2000/svg"
              fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
          </div>
        </div>

        <style>
          /* Fix for dark mode button background */
          .pure-black .pure-black-button {
            background-color: #09090b !important;
          }

          /* Keep the red color for the Cancel button in dark mode */
          .pure-black #cancel-export {
            background-color: #dc2626 !important;
          }

          /* Keep the primary color for the Export button in dark mode when enabled */
          .pure-black #execute-export:not([disabled]) {
            background-color: var(--primary) !important;
          }

          /* Add these styles inside the <style> tag in the export modal */
          #export-calendar {
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.2s ease, transform 0.2s ease;
            transform-origin: top left;
            will-change: opacity, transform;
          }

          #export-calendar.visible {
            opacity: 1;
            transform: scale(1);
          }
        </style>

        <script>
          // Direct DOM manipulation to ensure correct button background in dark mode
          document.addEventListener('DOMContentLoaded', function () {
            // Check if we're in dark mode
            if (document.body.classList.contains('pure-black')) {
              // Set the background color directly on the buttons
              const cancelBtn = document.getElementById('cancel-export');
              const exportBtn = document.getElementById('execute-export');
              const loadingBtn = document.getElementById('export-loading');

              if (cancelBtn) cancelBtn.style.backgroundColor = '#dc2626';
              if (exportBtn) {
                if (exportBtn.disabled) {
                  exportBtn.style.backgroundColor = '#09090b';
                } else {
                  exportBtn.style.backgroundColor = 'var(--primary)';
                }
              }
              if (loadingBtn) loadingBtn.style.backgroundColor = '#09090b';
            }

            // Add a mutation observer to detect theme changes
            const observer = new MutationObserver(function (mutations) {
              mutations.forEach(function (mutation) {
                if (mutation.attributeName === 'class') {
                  const isDarkMode = document.body.classList.contains('pure-black');
                  const cancelBtn = document.getElementById('cancel-export');
                  const exportBtn = document.getElementById('execute-export');
                  const loadingBtn = document.getElementById('export-loading');

                  if (isDarkMode) {
                    if (cancelBtn) cancelBtn.style.backgroundColor = '#dc2626';
                    if (exportBtn) {
                      if (exportBtn.disabled) {
                        exportBtn.style.backgroundColor = '#09090b';
                      } else {
                        exportBtn.style.backgroundColor = 'var(--primary)';
                      }
                    }
                    if (loadingBtn) loadingBtn.style.backgroundColor = '#09090b';
                  }
                }
              });
            });

            observer.observe(document.body, { attributes: true });
          });
        </script>
      </div>
    </div>

    <!-- Profile Icon with Dropdown -->
    <a class="inline-flex h-8 w-8 items-center justify-center rounded-full  ring-ring border-2  focus:outline-none "
      href="javascript:void(0);" role="button" aria-haspopup="true" aria-label="User Profile">
      <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full">
        <img class="aspect-square h-full w-full" alt="User Avatar"
          src="https://api.dicebear.com/8.x/lorelei/svg?seed=sveltecult">
      </span>
    </a>
    <div class="uk-drop uk-dropdown dropdown-content" uk-dropdown="mode: click; pos: bottom-right">
      <ul class="uk-dropdown-nav uk-nav">
        <li class="px-2 py-1.5 text-sm">
          <div class="flex flex-col space-y-1">
            <p class="text-sm font-medium leading-none">Guest Genius</p>
            <p class="text-xs leading-none text-muted-foreground">
              <EMAIL>
            </p>
          </div>
        </li>
        <li class="uk-nav-divider"></li>
        <li>
          <a class="justify-between language-option" href="#demo" role="button" data-lang="en">
            Profile
          </a>
        </li>
        <li>
          <a class="justify-between language-option" href="/issue" role="button" data-lang="en">
            Contact Support
          </a>
        </li>
        <li class="uk-nav-divider"></li>
        <li>
          <a class="uk-drop-close justify-between language-option" href="/logout" role="button" data-lang="en">
            Logout
          </a>
        </li>
      </ul>
    </div>
  </div>
  <script>
    // Complete export calendar functionality with floating popup
    document.addEventListener('DOMContentLoaded', function () {
      // Get export date elements
      const exportStartDateInput = document.getElementById('export-start-date');
      const exportEndDateInput = document.getElementById('export-end-date');
      const exportCalendar = document.getElementById('export-calendar');
      const exportMonthYearDisplay = document.getElementById('export-month-year');
      const exportCalendarGrid = document.getElementById('export-calendar-grid');
      const exportPrevMonthButton = document.getElementById('export-prev-month');
      const exportNextMonthButton = document.getElementById('export-next-month');
      const exportCancelButton = document.getElementById('export-cancel-date');
      const exportApplyButton = document.getElementById('export-apply-date');
      const exportModal = document.getElementById('export-modal');

      let exportCurrentDate = new Date(); // Track the displayed month/year
      let exportSelectedStartDate = null;
      let exportSelectedEndDate = null;
      let exportActiveInput = null; // Track which input is active

      // Make calendar a floating popup
      exportCalendar.style.position = 'absolute';
      exportCalendar.style.zIndex = '100';
      exportCalendar.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';


      // Function to position the calendar relative to the clicked input
      function positionCalendar(inputElement) {
        const inputRect = inputElement.getBoundingClientRect();
        const modalRect = document.querySelector('#export-modal .uk-modal-dialog').getBoundingClientRect();

        // Calculate position relative to the modal
        const top = inputRect.bottom - modalRect.top + 5;
        const left = inputRect.left - modalRect.left;

        exportCalendar.style.top = `${top}px`;
        exportCalendar.style.left = `${left}px`;
      }




      // Hide calendar with animation when clicking cancel
      exportCancelButton.addEventListener('click', function (e) {
        e.stopPropagation();
        hideCalendarWithAnimation();
      });

      // Apply the selected date with animation
      exportApplyButton.addEventListener('click', function (e) {
        e.stopPropagation();
        hideCalendarWithAnimation();

        // Rest of your existing apply logic here...
        if (exportStartDateInput.value && exportEndDateInput.value) {
          const exportBtn = document.getElementById('execute-export');
          exportBtn.disabled = false;
          exportBtn.classList.remove('opacity-50');

          // Update background color for dark mode
          if (document.body.classList.contains('pure-black')) {
            exportBtn.style.backgroundColor = 'var(--primary)';
          }
        }
      });

      // Close calendar when clicking outside with animation
      document.addEventListener('click', function (e) {
        if (exportCalendar && !exportCalendar.classList.contains('hidden') && !exportCalendar.classList.contains('animating')) {
          // Check if click is outside calendar and outside input fields
          if (!exportCalendar.contains(e.target) &&
            e.target !== exportStartDateInput &&
            e.target !== exportEndDateInput) {
            hideCalendarWithAnimation();
          }
        }
      });

      // Fixed animation for export calendar
      // Replace the current hideCalendarWithAnimation function with this improved version
      function hideCalendarWithAnimation() {
        if (exportCalendar.classList.contains('animating')) return; // Prevent duplicate animations

        exportCalendar.classList.add('animating');
        exportCalendar.classList.remove('visible');

        // Wait for animation to complete before hiding
        setTimeout(() => {
          exportCalendar.classList.add('hidden');
          exportCalendar.classList.remove('animating');

          // Force a reflow to ensure CSS state is reset
          void exportCalendar.offsetWidth;
        }, 200);
      }

      // Remove these duplicate event listeners
      // exportStartDateInput.addEventListener('click', function (e) { ... }); 
      // exportEndDateInput.addEventListener('click', function (e) { ... });

      // Instead, keep only these improved versions:
      function showCalendarWithAnimation(inputElement) {
        // First, completely reset the animation state
        exportCalendar.style.transition = 'none';
        exportCalendar.classList.remove('animating');
        exportCalendar.classList.remove('visible');
        exportCalendar.classList.add('hidden');

        // Force a reflow to ensure CSS state is reset completely
        void exportCalendar.offsetWidth;

        // Position the calendar properly
        positionCalendar(inputElement);

        // Re-enable transitions
        exportCalendar.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        exportCalendar.classList.remove('hidden');

        // Use double RAF for reliable animation
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            exportCalendar.classList.add('visible');
          });
        });

        renderExportCalendar(exportCurrentDate);
      }

      // Make sure we only have these event listeners (not duplicates)
      exportStartDateInput.addEventListener('click', function (e) {
        e.stopPropagation();
        exportActiveInput = 'start';
        showCalendarWithAnimation(this);
      });

      exportEndDateInput.addEventListener('click', function (e) {
        e.stopPropagation();
        exportActiveInput = 'end';
        showCalendarWithAnimation(this);
      });
      exportCalendar.addEventListener('click', function (e) {
        e.stopPropagation();
      });

      // Render export calendar (similar to existing calendar)
      function renderExportCalendar(date) {
        exportCalendarGrid.innerHTML = ''; // Clear previous grid
        const year = date.getFullYear();
        const month = date.getMonth();

        exportMonthYearDisplay.textContent = date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' });

        const firstDayOfMonth = new Date(year, month, 1);
        const lastDayOfMonth = new Date(year, month + 1, 0);
        const daysInMonth = lastDayOfMonth.getDate();
        const startDayOfWeek = firstDayOfMonth.getDay();

        // Add empty cells for days before the 1st of the month
        for (let i = 0; i < startDayOfWeek; i++) {
          const emptyCell = document.createElement('div');
          exportCalendarGrid.appendChild(emptyCell);
        }

        // Add date cells
        for (let day = 1; day <= daysInMonth; day++) {
          const dateCell = document.createElement('button');
          dateCell.textContent = day;
          dateCell.classList.add('text-sm', 'p-1', 'rounded', 'hover:bg-primary', 'hover:text-primary-foreground',
            'focus:outline-none', 'focus:ring-2', 'focus:ring-ring', 'focus:z-10');

          const cellDate = new Date(year, month, day);

          // Highlight today's date
          const todayDate = new Date();
          if (cellDate.toDateString() === todayDate.toDateString()) {
            dateCell.classList.add('bg-muted', 'font-semibold');
          }

          // Highlight start date
          if (exportSelectedStartDate && cellDate.toDateString() === exportSelectedStartDate.toDateString()) {
            dateCell.classList.add('bg-primary', 'text-primary-foreground', 'font-bold');
          }

          // Highlight end date
          if (exportSelectedEndDate && cellDate.toDateString() === exportSelectedEndDate.toDateString()) {
            dateCell.classList.add('bg-primary', 'text-primary-foreground', 'font-bold');
          }

          // Highlight dates in between start and end
          if (exportSelectedStartDate && exportSelectedEndDate &&
            cellDate > exportSelectedStartDate && cellDate < exportSelectedEndDate) {
            dateCell.classList.add('bg-primary/20');
          }

          // Date selection logic
          dateCell.addEventListener('click', function (e) {
            e.stopPropagation();

            if (exportActiveInput === 'start') {
              exportSelectedStartDate = cellDate;
              exportStartDateInput.value = formatDate(cellDate);

              // If end date is before start date, reset it
              if (exportSelectedEndDate && exportSelectedEndDate < exportSelectedStartDate) {
                exportSelectedEndDate = null;
                exportEndDateInput.value = '';
              }

              exportActiveInput = 'end';
              setTimeout(() => {
                exportEndDateInput.focus();
                positionCalendar(exportEndDateInput);
              }, 10);
            } else {
              // Don't allow end date before start date
              if (exportSelectedStartDate && cellDate < exportSelectedStartDate) {
                return;
              }

              exportSelectedEndDate = cellDate;
              exportEndDateInput.value = formatDate(cellDate);

              // Auto-apply if both dates are selected
              if (exportStartDateInput.value && exportEndDateInput.value) {
                setTimeout(() => {
                  exportCalendar.classList.add('hidden');
                  // Enable export button
                  const exportBtn = document.getElementById('execute-export');
                  exportBtn.disabled = false;
                  exportBtn.classList.remove('opacity-50');

                  // Update background color for dark mode
                  if (document.body.classList.contains('pure-black')) {
                    exportBtn.style.backgroundColor = 'var(--primary)';
                  }
                }, 200);
              }
            }

            renderExportCalendar(exportCurrentDate);
          });

          exportCalendarGrid.appendChild(dateCell);
        }
      }

      // Date navigation - prevent event propagation
      exportPrevMonthButton.addEventListener('click', function (e) {
        e.stopPropagation();
        exportCurrentDate.setMonth(exportCurrentDate.getMonth() - 1);
        renderExportCalendar(exportCurrentDate);
      });

      exportNextMonthButton.addEventListener('click', function (e) {
        e.stopPropagation();
        exportCurrentDate.setMonth(exportCurrentDate.getMonth() + 1);
        renderExportCalendar(exportCurrentDate);
      });

      // Date preset buttons - update to use animation
      document.querySelectorAll('.export-date-preset').forEach(button => {
        button.addEventListener('click', function () {
          // Existing preset logic...
          const days = this.getAttribute('data-days');
          const range = this.getAttribute('data-range');

          const endDate = new Date();
          let startDate = new Date();

          // Your existing date calculation logic...
          if (days) {
            startDate.setDate(startDate.getDate() - parseInt(days) + 1);
          } else if (range === 'this-month') {
            startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
          } else if (range === 'last-month') {
            endDate.setDate(0);
            startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
          } else if (range === 'this-year') {
            startDate = new Date(endDate.getFullYear(), 0, 1);
          } else if (range === 'all-time') {
            startDate = new Date(2020, 0, 1);
          }

          exportSelectedStartDate = startDate;
          exportSelectedEndDate = endDate;
          exportStartDateInput.value = formatDate(startDate);
          exportEndDateInput.value = formatDate(endDate);

          // Enable export button
          const exportBtn = document.getElementById('execute-export');
          exportBtn.disabled = false;
          exportBtn.classList.remove('opacity-50');

          // Update background color for dark mode
          if (document.body.classList.contains('pure-black')) {
            exportBtn.style.backgroundColor = 'var(--primary)';
          }

          // Hide the calendar if it's visible - with animation
          if (!exportCalendar.classList.contains('hidden')) {
            hideCalendarWithAnimation();
          }
        });
      });
      // Format export button selection
      document.querySelectorAll('.export-format-btn').forEach(button => {
        button.addEventListener('click', function () {
          // Remove active class from all format buttons
          document.querySelectorAll('.export-format-btn').forEach(btn => {
            btn.classList.remove('border-primary', 'bg-primary/5');
          });

          // Add active class to clicked button
          this.classList.add('border-primary', 'bg-primary/5');

          // Enable export button if dates are also selected
          if (exportStartDateInput.value && exportEndDateInput.value) {
            const exportBtn = document.getElementById('execute-export');
            exportBtn.disabled = false;
            exportBtn.classList.remove('opacity-50');

            // Update background color for dark mode
            if (document.body.classList.contains('pure-black')) {
              exportBtn.style.backgroundColor = 'var(--primary)';
            }
          }
        });
      });

      // Cancel export
      document.getElementById('cancel-export').addEventListener('click', function () {
        // Close modal
        UIkit.modal('#export-modal').hide();
      });

      // Execute export
      document.getElementById('execute-export').addEventListener('click', function () {
        if (this.disabled) return;

        // Hide the button and show loading state
        this.classList.add('hidden');
        document.getElementById('export-loading').classList.remove('hidden');

        // Simulate export process
        setTimeout(() => {
          // Hide loading and show button again
          document.getElementById('export-loading').classList.add('hidden');
          this.classList.remove('hidden');

          // Close modal
          UIkit.modal('#export-modal').hide();

          // Show success message
          UIkit.notification({
            message: 'Export completed successfully!',
            status: 'success',
            pos: 'bottom-right',
            timeout: 3000
          });
        }, 2000);
      });

      // Helper function to format date as MM/DD/YYYY
      function formatDate(date) {
        return date.toLocaleDateString('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric'
        });
      }

      // Ensure calendar is hidden when modal is hidden
      // Ensure calendar is hidden when modal is hidden - with animation
      UIkit.util.on('#export-modal', 'beforehide', function () {
        if (!exportCalendar.classList.contains('hidden')) {
          hideCalendarWithAnimation();
        }
      });
    });
  </script>
  <script>
    // Prevent default behavior for all links with href="#" to avoid page scrolling to top
    document.addEventListener('DOMContentLoaded', function () {
      document.querySelectorAll('a[href="#"]').forEach(function (link) {
        link.addEventListener('click', function (e) {
          e.preventDefault();
        });
      });

      // Initialize shadcn-style animations for dropdowns
      if (typeof UIkit !== 'undefined') {
        // Add event listeners for UIkit dropdown events
        UIkit.util.on('.dropdown-content', 'beforeshow', function () {
          // Before showing the dropdown, set initial state
          this.style.transformOrigin = 'top right';
          this.setAttribute('data-state', '');

          // Use requestAnimationFrame to ensure browser processes the DOM before animation
          requestAnimationFrame(() => {
            this.setAttribute('data-state', 'open');
          });
        });

        UIkit.util.on('.dropdown-content', 'beforehide', function () {
          // Start the fade out animation
          this.setAttribute('data-state', '');

          // Prevent the default hide behavior to allow our animation to finish
          const dropdown = this;
          const component = UIkit.dropdown(dropdown) || UIkit.drop(dropdown);

          if (component) {
            const originalHide = component.hide;
            component.hide = function () {
              // Do nothing, waiting for animation
            };

            // After animation completes, restore original hide method and call it
            setTimeout(() => {
              component.hide = originalHide;
              component.hide();
            }, 200); // Match animation duration
          }

          return false; // Prevent default hide
        });
      }
    });
  </script>
</div>