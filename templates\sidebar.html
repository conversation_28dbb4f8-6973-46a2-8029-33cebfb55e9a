<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../static/styles/custom.css">
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        .sidebar-transition {
            transition: width 0.3s ease, padding 0.3s ease;
        }
        .menu-item-transition {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .sidebar-label {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .hidden {
            transform: translateX(-10px);
        }
        .opacity-0 {
            opacity: 0;
        }

        /* Updated Partition Styles */
        .partition {
            display: block;
            height: 1px;
            background-color: #27272a;
            margin: 0 -16px; /* Remove vertical margins, keep horizontal */
            width: calc(100% + 32px);
            position: relative;
            left: 0;
        }

        #sidebar.collapsedsidebar .partition {
            margin: 0 -16px; /* Remove vertical margins, keep horizontal */
            width: calc(100% + 32px);
        }

        /* Center icons when sidebar is collapsed */
        .w-20 .sidebar-link {
            justify-content: center;
        }
        /* Adjust the logo container */
        .logo-container {
            transition: all 0.3s ease;
        }
        .w-20 .logo-container {
            justify-content: center;
        }
        
        .grid {
            transition: grid-template-columns 0.3s ease;
        }
        .grid.collapsed {
            grid-template-columns: 60px 1fr;
        }
    
        /* Hide scrollbar for sidebar */
        #sidebar::-webkit-scrollbar {
            display: none;
        }
        #sidebar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        #logo-img {
            min-width: 40px !important;
            min-height: 40px !important;
        }
        .logo-image.collapsed {
            width: 20px;
            height: 20px;
        }

        /* Custom styles for toggle button positioning */
        #toggle-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            /* padding: 0.8rem; */
        }

        /* Update label color */
        .text-gray-500 {
            color: rgb(107 114 128) !important;
            font-size: 0.75rem !important;
        }

        /* Ensure existing scrollbar hiding styles are working */
        #sidebar::-webkit-scrollbar,
        .flex-1::-webkit-scrollbar {
            display: none;
            width: 0;
        }

        /* Sidebar link styles */
        .sidebar-link {
            position: relative;
            transition: background-color 0.2s ease;
        }

        .sidebar-link:hover {
            background-color: var(--dropdown-hover-bg);
            color: var(--accent-foreground);
        }

        .sidebar-link.active {
            background-color: var(--dropdown-hover-bg);
            color: var(--accent-foreground);
            font-weight: 500;
            box-shadow: inset 3px 0 0 var(--primary); /* subtle left border */
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            transform: scale(1.02);
        }

        .sidebar-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--primary);
            border-radius: 0 4px 4px 0;
        }

        /* Adjust spacing around section headers */
        nav .flex-col > div:not(:first-child) {
            margin-top: 8px;
        }
        
        
        #sidebar {
            position: sticky;
            top: 0;
            height: 100vh;
            /* Force full viewport height */
            overflow-y: auto;
            /* Allow scrolling within the sidebar */
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body class="bg-background text-foreground">
    <div id="sidebar" class="border-r card sidebar-transition w-full">
        <div class="flex h-full flex-col">
            <!-- Header Section -->
            <div class="flex h-[60px] items-center border-b px-2 card justify-between group">
                <a class="flex items-center gap-2 font-semibold logo-container" href="#">
                    <img src="../static/images/logo.png" class="logo-image w-5" alt="Guest Genius Logo" id="logo-img">
                    <span class="text-base sidebar-label" id="brand-text">Guest Genius</span>
                </a>

            </div>
            
            <!-- Navigation -->
            <div class="flex-1 overflow-auto py-2">
                <nav class="flex flex-col gap-4 px-3">
                    <!-- Core Section -->
                    <div>
                        <div class="mt-4 flex items-center justify-between px-2">
                            <h3 class="mb-0 text-sm font-semibold text-gray-500 sidebar-label">CORE</h3>
                        </div>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                </svg>
                                <span class="sidebar-label">Dashboard</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/users">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                <span class="sidebar-label">Users</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/products">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shopping-cart">
                                    <circle cx="8" cy="21" r="1"/>
                                    <circle cx="19" cy="21" r="1"/>
                                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
                                </svg>
                                <span class="sidebar-label">Products</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/analytics">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 3v18h18"></path>
                                    <path d="m19 9-5 5-4-4-3 3"></path>
                                </svg>
                                <span class="sidebar-label">Overview</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition lines"></div>

                    <!-- Analytics Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">ANALYTICS & REPORTING</h3>
                        <div class="flex flex-col gap-1">

                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/sales">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" x2="12" y1="2" y2="22"></line>
                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                </svg>
                                <span class="sidebar-label">Sales Reports</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/pmsanalytics">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 3h7v7H3z"></path>
                                    <path d="M14 3h7v7h-7z"></path>
                                    <path d="M14 14h7v7h-7z"></path>
                                    <path d="M3 14h7v7H3z"></path>
                                </svg>
                                <span class="sidebar-label">PMS Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/googleana">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="4" y1="20" x2="4" y2="10"></line>
                                    <line x1="12" y1="20" x2="12" y2="4"></line>
                                    <line x1="20" y1="20" x2="20" y2="14"></line>
                                </svg>
                                <span class="sidebar-label">Google Analytics</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/status">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                    <path d="M9 12l2 2 4-4"></path>
                                </svg>
                                <span class="sidebar-label">Status Report</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition lines"></div>

                    <!-- Communication Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">COMMUNICATION</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/livechat">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                                </svg>
                                <span class="sidebar-label">Omni Channel</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/aichat">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot-message-square">
                                    <path d="M12 6V2H8"/>
                                    <path d="m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z"/>
                                    <path d="M2 12h2"/>
                                    <path d="M9 11v2"/>
                                    <path d="M15 11v2"/>
                                    <path d="M20 12h2"/>
                                </svg>
                                <span class="sidebar-label">Guest Genius</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/voicebot">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone-call">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    <path d="M14.05 2a9 9 0 0 1 8 7.94"/>
                                    <path d="M14.05 6A5 5 0 0 1 18 10"/>
                                </svg>
                                <span class="sidebar-label">Voice Agents</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition lines"></div>

                    <!-- Management Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">MANAGEMENT</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/tasks">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>
                                <span class="sidebar-label">Tasks</span>
                            </a>
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/issue">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" x2="12" y1="8" y2="12"></line>
                                    <line x1="12" x2="12.01" y1="16" y2="16"></line>
                                </svg>
                                <span class="sidebar-label">Issues</span>
                            </a>
                        </div>
                    </div>

                    <!-- Partition -->
                    <div class="partition lines card"></div>

                    <!-- Settings Section -->
                    <div>
                        <h3 class="mb-1 px-2 text-sm font-semibold text-gray-500 sidebar-label">SETTINGS</h3>
                        <div class="flex flex-col gap-1">
                            <a class="sidebar-link flex items-center gap-3 rounded-md px-2 py-1.5 text-sm menu-item-transition" href="/settings">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                <span class="sidebar-label">Settings</span>
                            </a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </div>

</body>
</html>